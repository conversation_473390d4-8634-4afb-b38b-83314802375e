import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  console.error(err);
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

export default app;
