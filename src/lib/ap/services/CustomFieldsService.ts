import { APCustomFieldRequest } from "@libAP/requests";

export class CustomFieldsService {
  private request: APCustomFieldRequest;
  private LocalPatient: TLocalPatient;

  constructor(patient: TLocalPatient) {
    this.request = new APCustomFieldRequest();
    this.LocalPatient = patient;
  }

  async appointmentCustomFields() {}

  async invoiceCustomFields() {}

  async paymentCustomFields() {}

  async getAllCustomFields() {
    return await this.request.all();
  }

  async getNameValue(): Promise<IKeyValue[]> {
    if (!this.LocalPatient.apData?.customFields?.length) {
      return [] as IKeyValue[];
    }
    const allFields = await this.getAllCustomFields();
    const returnFields: IKeyValue[] = [];
    this.LocalPatient.apData.customFields.map((field) => {
      const customField = allFields.find((f) => f.id === field.id);
      customField && returnFields.push({ [customField.name]: field.value });
    });
    return returnFields;
  }
}
