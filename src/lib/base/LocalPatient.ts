import { dbSchema, getDb } from "@database";
import type { GetAPContactType } from "@libAP/APTypes";
import type { GetCCPatientType } from "@libCC/CCTypes";
import { eq, type SQLWrapper } from "drizzle-orm";
import { logger } from "@/utils";

export type TLocalPatient = typeof dbSchema.patient.$inferSelect;

export class LocalPatient {
  protected query = getDb().query.patient;
  protected db = getDb();
  public patient: TLocalPatient | undefined;
  public ccPatient: GetCCPatientType | undefined | null;
  public apContact: GetAPContactType | undefined | null;
  protected log = logger;
  protected payload: GetCCPatientType | GetAPContactType | null = null;
  protected dbSchema: typeof dbSchema;

  constructor() {
    this.query = getDb().query.patient;
    this.db = getDb();
    this.dbSchema = dbSchema;
  }

  async getPatientById(id: string): Promise<TLocalPatient | undefined> {
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.id, id),
    });
    this.ccPatient = this.patient?.ccData;
    this.apContact = this.patient?.apData;
    return this.patient;
  }

  async getPatientByCCId(id: number): Promise<TLocalPatient | undefined> {
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.ccId, id),
    });
    this.ccPatient = this.patient?.ccData;
    this.apContact = this.patient?.apData;
    return this.patient;
  }

  async getPatientByAPId(id: string): Promise<TLocalPatient | undefined> {
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.apId, id),
    });
    this.ccPatient = this.patient?.ccData;
    this.apContact = this.patient?.apData;
    return this.patient;
  }

  async getPatientByEmail(email: string): Promise<TLocalPatient | undefined> {
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.email, email),
    });
    this.ccPatient = this.patient?.ccData;
    this.apContact = this.patient?.apData;
    return this.patient;
  }

  async getPatientByPhone(phone: string): Promise<TLocalPatient | undefined> {
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.phone, phone),
    });
    this.ccPatient = this.patient?.ccData;
    this.apContact = this.patient?.apData;
    return this.patient;
  }

  async upsertLocalFromCC(patient: GetCCPatientType) {
    this.payload = patient;
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.ccId, patient.id),
    });

    if (this.patient) {
      this.ccPatient = this.patient.ccData;
      this.apContact = this.patient.apData;
      return this.patient;
    }

    const create = await this.db
      .insert(dbSchema.patient)
      .values({
        ccId: patient.id,
        ccData: patient,
        ccUpdatedAt: new Date(patient.updatedAt),
      })
      .returning();

    this.patient = create[0];
    this.ccPatient = this.patient.ccData;
    this.apContact = this.patient.apData;
    return this.patient;
  }

  async upsertLocalFromAP(contact: GetAPContactType) {
    this.payload = contact;
    this.patient = await this.query.findFirst({
      where: eq(dbSchema.patient.apId, contact.id),
    });

    if (this.patient) {
      this.ccPatient = this.patient.ccData;
      this.apContact = this.patient.apData;
      return this.patient;
    }

    const create = await this.db
      .insert(dbSchema.patient)
      .values({
        apId: contact.id,
        apData: contact,
        apUpdatedAt: new Date(contact.dateUpdated || new Date()),
      })
      .returning();

    this.patient = create[0];
    this.ccPatient = this.patient.ccData;
    this.apContact = this.patient.apData;
    return this;
  }

  async refresh(id?: string): Promise<TLocalPatient | undefined> {
    if (id) {
      this.patient = await this.query.findFirst({
        where: eq(dbSchema.patient.id, id),
      });
      return this.patient;
    }
    if (this.patient) {
      this.patient = await this.query.findFirst({
        where: eq(dbSchema.patient.id, this.patient.id),
      });
      this.ccPatient = this.patient?.ccData;
      this.apContact = this.patient?.apData;
      return this.patient;
    }
    this.log.debug("Patient not found, While refreshing", {
      patientId: id,
      patient: this.patient,
    });
    throw new Error("Patient not found");
  }

  async getPayload() {
    return this.payload;
  }

  async findBy(
    name: keyof typeof dbSchema.patient.$inferSelect,
    value:
      | string
      | number
      | Date
      | GetAPContactType
      | GetCCPatientType
      | SQLWrapper
  ) {
    return await this.query.findFirst({
      where: eq(dbSchema.patient[name], value),
    });
  }
}
