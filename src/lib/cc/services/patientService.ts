import type { dbSchema } from "@database";
import type { GetCCPatientType } from "@libCC/CCTypes";
import { CCPatientRequest } from "@libCC/request/patientRequest";
import { LocalPatient } from "@/lib/base/LocalPatient";
import { ContactService } from "@libAP";

export class PatientService extends LocalPatient {
  private request: CCPatientRequest;
  private contactService: ContactService;

  constructor() {
    super();
    this.request = new CCPatientRequest();
    this.contactService = new ContactService();
  }

  async sync(patient: GetCCPatientType) {
    const localPatient = await this.getPatientByCCId(patient.id);
    if (!localPatient) {
      await this.upsertLocalFromCC(patient);
    }
    if (await this.isInBuffer(patient)) {
      this.log.info("Patient is in buffer, Skipping sync", {
        patientId: patient.id,
        patient,
      });
      return this;
    }
    localPatient && (await this.contactService.upsertToAP(localPatient));
    // TODO: Sync custom fields
    return this;
  }

  async searchPatientInCC(
    searchTerm: string | string[]
  ): Promise<typeof dbSchema.patient.$inferSelect | undefined> {
    let patient: GetCCPatientType | null = null;
    if (Array.isArray(searchTerm)) {
      // Search with multiple terms and return the first match found
      for (const term of searchTerm) {
        const result = await this.request.search(term);
        if (result !== null) {
          patient = result; // Return first match found
          break;
        }
      }
    } else {
      patient = await this.request.search(searchTerm);
    }
    patient && (await this.upsertLocalFromCC(patient));
    return this.patient;
  }

  async getPatientByIdFromCC(id: number) {
    const patient = await this.request.get(id);
    patient && (await this.upsertLocalFromCC(patient));
    return this.patient;
  }

  async isInBuffer(payload: GetCCPatientType): Promise<boolean> {
    const payloadUpdatedAt = payload.updatedAt;
    const localUpdatedAt = this.patient?.ccUpdatedAt;

    // If no local timestamp exists, not in buffer
    if (!localUpdatedAt) {
      return false;
    }

    // Convert payload timestamp to Date object
    const payloadDate = new Date(payloadUpdatedAt);
    const localDate = new Date(localUpdatedAt);

    // Calculate the absolute difference in milliseconds
    // This handles both cases: payload ahead or behind local timestamp
    const timeDifferenceMs = Math.abs(
      payloadDate.getTime() - localDate.getTime()
    );

    // Convert 1 minute to milliseconds (60 * 1000 = 60000)
    const oneMinuteMs = 60 * 1000;

    // Return true if within 1 minute buffer (ahead OR behind), false if outside buffer
    return timeDifferenceMs <= oneMinuteMs;
  }
}
